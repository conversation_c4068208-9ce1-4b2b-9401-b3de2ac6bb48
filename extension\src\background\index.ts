/**
 * EchoSync Background Script
 * Service Worker - 负责数据库操作的中转和消息处理
 */

import { dexieDatabase } from '../lib/database/dexie'
import { chatHistoryService } from '../lib/dao/chatHistoryDexie'
import { platformService } from '../lib/dao/platformDexie'
import { StorageService } from '../lib/service/storage'
import { MessagingService } from '../lib/service/messagingService'
import { MessageType, ChromeMessage } from '../types'
import { databaseConnectionManager } from './databaseConnection'
import { keepAliveManager } from './keepAlive'
import { healthMonitor } from './healthMonitor'

console.log('【EchoSync】Service Worker loaded')

// 统一初始化函数
async function initializeServiceWorker(): Promise<void> {
  try {
    console.log('【EchoSync】Starting Service Worker initialization...')

    // 初始化数据库连接管理器
    const dbSuccess = await databaseConnectionManager.initialize()
    if (!dbSuccess) {
      throw new Error('Database initialization failed')
    }
    console.log('【EchoSync】✓ Database initialized successfully')

    // 初始化保活管理器
    await keepAliveManager.initialize()
    console.log('【EchoSync】✓ Keep-alive manager initialized successfully')

    console.log('【EchoSync】Service Worker initialization completed successfully')
  } catch (error) {
    console.error('【EchoSync】Service Worker initialization failed:', error)
    throw error
  }
}



// 扩展安装时初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('【EchoSync】Extension installed:', details.reason)

  try {
    await initializeServiceWorker()

    if (details.reason === 'install') {
      // 首次安装，设置默认配置
      const settings = await StorageService.getSettings()
      await StorageService.saveSettings(settings)

      // 打开欢迎页面
      chrome.tabs.create({
        url: chrome.runtime.getURL('options/index.html?welcome=true')
      })
    }
  } catch (error) {
    console.error('【EchoSync】Installation setup failed:', error)
  }
})

// Service Worker启动时初始化
chrome.runtime.onStartup.addListener(async () => {
  console.log('【EchoSync】Service Worker started')
  try {
    await initializeServiceWorker()
  } catch (error) {
    console.error('【EchoSync】Startup initialization failed:', error)
  }
})

// 消息处理
MessagingService.onMessage(async (message: ChromeMessage, sender, sendResponse) => {
  console.log('【EchoSync】Background received message:', message.type)

  try {
    // 确保数据库连接就绪
    const isReady = await databaseConnectionManager.ensureConnection()
    if (!isReady) {
      throw new Error('Database connection not ready')
    }

    switch (message.type) {
      // 聊天历史相关操作
      case MessageType.DB_CHAT_HISTORY_CREATE:
        const createResult = await chatHistoryService.create(message.payload)
        sendResponse(createResult)
        break

      case MessageType.DB_CHAT_HISTORY_GET_LIST:
        const listResult = await chatHistoryService.getList(message.payload)
        sendResponse(listResult)
        break

      case MessageType.DB_CHAT_HISTORY_GET_UNIQUE:
        const uniqueResult = await chatHistoryService.getUniqueChats(message.payload)
        sendResponse(uniqueResult)
        break

      case MessageType.DB_CHAT_HISTORY_SEARCH:
        const searchResult = await chatHistoryService.search(message.payload.searchTerm, message.payload.params)
        sendResponse(searchResult)
        break

      case MessageType.DB_CHAT_HISTORY_UPDATE:
        const updateResult = await chatHistoryService.update(message.payload.id, message.payload.data)
        sendResponse(updateResult)
        break

      case MessageType.DB_CHAT_HISTORY_DELETE:
        const deleteResult = await chatHistoryService.delete(message.payload.id)
        sendResponse(deleteResult)
        break

      case MessageType.DB_CHAT_HISTORY_GET_BY_UID:
        const uidResult = await chatHistoryService.getByChatUid(message.payload.chatUid)
        sendResponse(uidResult)
        break

      // 平台相关操作
      case MessageType.DB_PLATFORM_GET_BY_NAME:
        const platformByNameResult = await platformService.getByName(message.payload.name)
        sendResponse(platformByNameResult)
        break

      case MessageType.DB_PLATFORM_GET_BY_DOMAIN:
        const platformByDomainResult = await platformService.findByDomain(message.payload.hostname)
        sendResponse(platformByDomainResult)
        break

      case MessageType.DB_PLATFORM_GET_LIST:
        const platformListResult = await platformService.getAll()
        sendResponse(platformListResult)
        break

      // 兼容旧接口
      case MessageType.GET_HISTORY:
        const historyResult = await chatHistoryService.getUniqueChats({ limit: 100 })
        if (historyResult.success) {
          sendResponse({ success: true, data: historyResult.data })
        } else {
          sendResponse({ success: false, error: historyResult.error })
        }
        break

      case MessageType.SAVE_CONVERSATION:
        await StorageService.addConversation(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.UPDATE_SETTINGS:
        await StorageService.saveSettings(message.payload)
        sendResponse({ success: true })
        break

      // 提示词同步和捕获
      case MessageType.SYNC_PROMPT:
        await handleSyncPrompt(message.payload, sender)
        sendResponse({ success: true })
        break

      case MessageType.CAPTURE_PROMPT:
        await handleCapturePrompt(message.payload)
        sendResponse({ success: true })
        break

      // Favicon相关操作
      case MessageType.UPDATE_PLATFORM_FAVICON:
        await handleUpdatePlatformFavicon(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.CHECK_PLATFORM_FAVICON:
        console.log('【Background】收到CHECK_PLATFORM_FAVICON消息:', message.payload)
        const faviconCheckResult = await handleCheckPlatformFavicon(message.payload)
        console.log('【Background】CHECK_PLATFORM_FAVICON处理结果:', faviconCheckResult)
        sendResponse(faviconCheckResult)
        break

      default:
        // 处理非MessageType的消息（如SW状态检查）
        if ((message as any).type === 'SW_PING') {
          sendResponse({ success: true, timestamp: Date.now(), status: 'alive' })
        } else if ((message as any).type === 'SW_WAKE_UP') {
          console.log('【EchoSync】Service Worker wake-up signal received')
          sendResponse({ success: true, timestamp: Date.now(), status: 'awake' })
        } else if ((message as any).type === 'SW_HEALTH_CHECK') {
          const healthStatus = await healthMonitor.performHealthCheck()
          sendResponse({ success: true, data: healthStatus })
        } else if ((message as any).type === 'SW_SYSTEM_STATUS') {
          const systemStatus = await healthMonitor.getSystemStatus()
          sendResponse({ success: true, data: systemStatus })
        } else {
          sendResponse({ success: false, error: 'Unknown message type' })
        }
    }
  } catch (error) {
    console.error('【EchoSync】Message handling error:', error)
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// 辅助函数：获取或创建平台
async function getPlatform(platformName: string) {
  if (!platformName) return null

  const name = platformName.charAt(0).toUpperCase() + platformName.slice(1).toLowerCase()
  let result = await platformService.getByName(name)

  if (result.success) return result.data

  // 尝试创建平台
  const urls: Record<string, string> = {
    'Deepseek': 'https://chat.deepseek.com',
    'Chatgpt': 'https://chat.openai.com',
    'Claude': 'https://claude.ai',
    'Gemini': 'https://gemini.google.com',
    'Kimi': 'https://kimi.moonshot.cn'
  }

  if (urls[name]) {
    const createResult = await platformService.create({ name, url: urls[name] })
    return createResult.success ? createResult.data : null
  }

  return null
}

// 辅助函数：处理平台favicon更新
async function handleUpdatePlatformFavicon(payload: {
  platformId: number
  faviconData: number[]
  faviconType: string
  faviconUrl?: string
}): Promise<void> {
  try {
    console.log(`【Background-平台${payload.platformId}】接收到favicon更新请求:`, {
      dataLength: payload.faviconData.length,
      type: payload.faviconType,
      url: payload.faviconUrl
    })

    // 将数组转换回Blob
    const uint8Array = new Uint8Array(payload.faviconData)
    const faviconBlob = new Blob([uint8Array], { type: payload.faviconType })

    console.log(`【Background-平台${payload.platformId}】重构Blob成功:`, {
      size: faviconBlob.size,
      type: faviconBlob.type
    })

    const updateData: any = {
      icon_blob: faviconBlob
    }

    if (payload.faviconUrl) {
      updateData.icon = payload.faviconUrl
    }

    console.log(`【Background-平台${payload.platformId}】准备更新数据库:`, {
      hasBlob: !!updateData.icon_blob,
      blobSize: updateData.icon_blob?.size,
      iconUrl: updateData.icon
    })

    const result = await platformService.update(payload.platformId, updateData)

    if (result.success) {
      console.log(`【Background-平台${payload.platformId}】数据库更新成功`)

      // 验证存储结果
      const verifyResult = await platformService.getById(payload.platformId)
      if (verifyResult.success && verifyResult.data) {
        console.log(`【Background-平台${payload.platformId}】存储验证:`, {
          hasIconBlob: !!verifyResult.data.icon_blob,
          iconBlobType: typeof verifyResult.data.icon_blob,
          iconBlobSize: verifyResult.data.icon_blob?.size || 'N/A'
        })
      }
    } else {
      console.error(`【Background-平台${payload.platformId}】数据库更新失败:`, result.error)
    }
  } catch (error) {
    console.error(`【Background-平台${payload.platformId}】处理favicon更新异常:`, error)
  }
}

// 辅助函数：检查平台是否需要更新favicon
async function handleCheckPlatformFavicon(payload: {
  platformId: number
}): Promise<{ success: boolean; data?: { needsUpdate: boolean; reason?: string }; error?: string }> {
  try {
    console.log(`【Background-平台${payload.platformId}】开始检查favicon更新需求`)

    const platformResult = await platformService.getById(payload.platformId)

    if (!platformResult.success) {
      console.error(`【Background-平台${payload.platformId}】获取平台信息失败:`, platformResult.error)
      return {
        success: false,
        error: platformResult.error || 'Platform not found'
      }
    }

    const platform = platformResult.data!
    console.log(`【Background-平台${payload.platformId}】平台信息:`, {
      name: platform.name,
      hasIconBlob: !!platform.icon_blob,
      iconBlobType: typeof platform.icon_blob,
      iconBlobConstructor: platform.icon_blob?.constructor?.name,
      iconBlobSize: platform.icon_blob?.size || 'N/A'
    })

    // 检查icon_blob是否存在且为有效的Blob对象
    if (!platform.icon_blob || !(platform.icon_blob instanceof Blob)) {
      const reason = !platform.icon_blob ? 'missing' : 'invalid_type'
      console.log(`【Background-平台${payload.platformId}】icon_blob无效，需要更新:`, {
        exists: !!platform.icon_blob,
        type: typeof platform.icon_blob,
        isBlob: platform.icon_blob instanceof Blob,
        constructor: (platform.icon_blob as any)?.constructor?.name,
        reason: reason
      })
      return {
        success: true,
        data: { needsUpdate: true, reason: reason }
      }
    }

    // 检查Blob大小
    if (platform.icon_blob.size === 0) {
      console.log(`【Background-平台${payload.platformId}】icon_blob大小为0，需要更新`)
      return {
        success: true,
        data: { needsUpdate: true, reason: 'empty_blob' }
      }
    }

    console.log(`【Background-平台${payload.platformId}】Blob对象有效，开始验证数据完整性`)

    // 验证icon_blob数据有效性
    try {
      // 将Blob转换为dataURL进行验证
      const dataUrl = await blobToDataUrl(platform.icon_blob)
      console.log(`【Background-平台${payload.platformId}】Blob转DataURL成功，长度:`, dataUrl.length)

      const isValidBlob = await validateIconBlob(dataUrl)
      if (!isValidBlob) {
        console.log(`【Background-平台${payload.platformId}】icon_blob数据验证失败，需要更新`)
        return {
          success: true,
          data: { needsUpdate: true, reason: 'invalid_data' }
        }
      }

      console.log(`【Background-平台${payload.platformId}】icon_blob数据验证通过`)
    } catch (error) {
      console.log(`【Background-平台${payload.platformId}】icon_blob转换失败，需要更新:`, error)
      return {
        success: true,
        data: { needsUpdate: true, reason: 'conversion_failed' }
      }
    }

    console.log(`【Background-平台${payload.platformId}】favicon检查完成，无需更新:`, {
      platformName: platform.name,
      blobSize: platform.icon_blob.size,
      blobType: platform.icon_blob.type
    })

    return {
      success: true,
      data: { needsUpdate: false }
    }
  } catch (error) {
    console.error('【EchoSync】检查平台favicon更新需求异常:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 验证icon_blob数据的有效性
 */
/**
 * 将Blob转换为DataURL
 */
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
    reader.readAsDataURL(blob)
  })
}

async function validateIconBlob(iconBlob: string): Promise<boolean> {
  try {
    // 检查数据是否为空
    if (!iconBlob || iconBlob.trim().length === 0) {
      return false
    }

    // 检查是否为有效的base64数据URL格式
    if (!iconBlob.startsWith('data:image/')) {
      return false
    }

    // 提取base64数据部分
    const base64Match = iconBlob.match(/^data:image\/[^;]+;base64,(.+)$/)
    if (!base64Match) {
      return false
    }

    const base64Data = base64Match[1]
    
    // 检查base64数据长度是否合理（避免空数据或异常大数据）
    if (base64Data.length < 100) { // 太小，可能是无效数据
      return false
    }
    
    if (base64Data.length > 1024 * 1024) { // 大于1MB，可能异常
      return false
    }

    // 尝试解码base64数据验证格式正确性
    try {
      atob(base64Data)
    } catch {
      return false // base64解码失败
    }

    // 验证MIME类型
    const mimeMatch = iconBlob.match(/^data:image\/([^;]+)/)
    if (mimeMatch) {
      const mimeType = mimeMatch[1].toLowerCase()
      const validMimeTypes = ['png', 'jpeg', 'jpg', 'gif', 'svg+xml', 'webp', 'x-icon', 'vnd.microsoft.icon']
      if (!validMimeTypes.includes(mimeType)) {
        return false
      }
    }

    return true
  } catch (error) {
    console.error('【EchoSync】验证icon_blob数据异常:', error)
    return false
  }
}

// 辅助函数：同步到其他标签页
async function syncToOtherTabs(content: string, excludeTabId?: number) {
  const tabs = await chrome.tabs.query({
    url: ['https://chat.openai.com/*', 'https://chat.deepseek.com/*', 'https://claude.ai/*',
          'https://gemini.google.com/*', 'https://kimi.moonshot.cn/*']
  })

  const promises = tabs
    .filter(tab => tab.id !== excludeTabId)
    .map(tab => tab.id ? MessagingService.sendToContentScript(
      tab.id, MessageType.INJECT_PROMPT, { prompt: content }
    ).catch(() => {}) : Promise.resolve())

  await Promise.all(promises)
}

// 处理提示词同步
async function handleSyncPrompt(promptData: any, sender: any) {
  try {
    const platform = await getPlatform(promptData.platform)
    if (!platform) return

    // 保存提示词
    const result = await chatHistoryService.create({
      chat_prompt: promptData.content,
      platform_id: platform.id!,
      chat_uid: Date.now().toString(),
      create_time: promptData.timestamp || Date.now()
    })

    if (result.success) {
      console.log('【EchoSync】Prompt saved:', result.data?.id)

      // 检查是否启用同步
      const settings = await StorageService.getSettings()
      if (settings.syncEnabled) {
        await syncToOtherTabs(promptData.content, sender.tab?.id)
      }
    }
  } catch (error) {
    console.error('【EchoSync】Sync prompt error:', error)
  }
}

// 处理提示词捕获
async function handleCapturePrompt(data: any) {
  try {
    const platform = await getPlatform(data.platform)
    if (!platform) return

    const result = await chatHistoryService.create({
      chat_prompt: data.content,
      platform_id: platform.id!,
      chat_uid: Date.now().toString(),
      create_time: Date.now()
    })

    if (result.success) {
      console.log('【EchoSync】Prompt captured:', result.data?.id)

      // 通知popup更新
      chrome.runtime.sendMessage({
        type: 'PROMPT_CAPTURED',
        payload: { id: result.data?.id, content: data.content, platform: data.platform, timestamp: Date.now() }
      }).catch(() => {})
    }
  } catch (error) {
    console.error('【EchoSync】Capture prompt error:', error)
  }
}

// 监听标签页更新，注入content script
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const supportedDomains = ['chat.openai.com', 'chat.deepseek.com', 'claude.ai', 'gemini.google.com', 'kimi.moonshot.cn']
    const isSupported = supportedDomains.some(domain => tab.url && tab.url.includes(domain))

    if (isSupported) {
      chrome.scripting.executeScript({ target: { tabId }, files: ['content/index.js'] }).catch(() => {})
    }
  }
})

// 处理快捷键
chrome.commands.onCommand.addListener(async (command) => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
  if (!tab.id) return

  if (command === 'open-popup') {
    chrome.action.openPopup()
  } else if (command === 'quick-sync') {
    MessagingService.sendToContentScript(tab.id, MessageType.CAPTURE_PROMPT, {}).catch(() => {})
  }
})

// 在开发环境中启动健康监控
if (process.env.NODE_ENV === 'development') {
  healthMonitor.startPeriodicHealthCheck()
}
