/**
 * Content页面Favicon服务
 * 只在对应平台的content页面执行favicon获取，避免跨域问题
 */

import { MessagingService } from './messagingService'
import { MessageType } from '@/types'

export interface ContentFaviconResult {
  success: boolean
  blob?: Blob
  dataUrl?: string
  error?: string
  source?: string
}

export class ContentFaviconService {
  private static instance: ContentFaviconService
  // 防重复下载机制：跟踪正在下载favicon的平台
  private static downloadingPlatforms: Set<number> = new Set()
  // 内存缓存：缓存检查结果，避免短时间内重复检查
  private static checkCache: Map<number, { result: boolean; timestamp: number }> = new Map()
  private static readonly CACHE_DURATION = 30000 // 30秒缓存时间
  
  // 批量处理机制
  private static batchQueue = new Map<number, Promise<boolean>>()
  private static readonly BATCH_DELAY = 100 // 100ms批量延迟
  private static batchTimer: NodeJS.Timeout | null = null
  
  // 性能统计
  private static stats = {
    totalRequests: 0,
    cacheHits: 0,
    successfulUpdates: 0,
    failedUpdates: 0,
    averageProcessTime: 0,
    lastResetTime: Date.now()
  }

  private constructor() {}

  static getInstance(): ContentFaviconService {
    if (!ContentFaviconService.instance) {
      ContentFaviconService.instance = new ContentFaviconService()
    }
    return ContentFaviconService.instance
  }

  /**
   * 在当前页面获取favicon（只在content script中调用）
   */
  async getCurrentPageFavicon(): Promise<ContentFaviconResult> {
    try {
      console.log('【ContentFaviconService】开始获取当前页面favicon')
      
      // 获取当前页面的favicon
      const faviconUrl = await this.findCurrentPageFavicon()
      if (!faviconUrl) {
        return {
          success: false,
          error: 'No favicon found on current page'
        }
      }

      console.log('【ContentFaviconService】找到favicon URL:', faviconUrl)

      // 获取favicon数据
      const response = await fetch(faviconUrl)
      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        }
      }

      const blob = await response.blob()
      
      // 验证是否为有效的图片
      if (!this.isValidImageBlob(blob)) {
        return {
          success: false,
          error: 'Invalid image format'
        }
      }

      // 可选的深度验证（检查图片完整性）
      const isIntegrityValid = await this.validateBlobIntegrity(blob)
      if (!isIntegrityValid) {
        console.warn('【ContentFaviconService】Blob完整性验证失败，但继续处理')
        // 注意：这里不直接返回失败，因为某些情况下验证可能误报
      }

      // 转换为data URL
      const dataUrl = await this.blobToDataUrl(blob)

      console.log('【ContentFaviconService】成功获取favicon，大小:', blob.size, 'bytes')

      return {
        success: true,
        blob,
        dataUrl,
        source: faviconUrl
      }

    } catch (error) {
      console.error('【ContentFaviconService】获取favicon失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 查找当前页面的favicon
   */
  private async findCurrentPageFavicon(): Promise<string | null> {
    console.log('【ContentFaviconService】开始查找当前页面favicon')
    
    // 1. 优先从HTML head中查找，按优先级排序
    const faviconCandidates = this.getFaviconCandidatesFromHead()
    
    for (const candidate of faviconCandidates) {
      if (await this.testFaviconUrl(candidate.url)) {
        console.log('【ContentFaviconService】找到有效favicon:', candidate.url, '类型:', candidate.type)
        return candidate.url
      }
    }

    // 2. 尝试从页面Sources中查找图标资源
    const sourceIcon = await this.findIconFromSources()
    if (sourceIcon) {
      console.log('【ContentFaviconService】从Sources找到图标:', sourceIcon)
      return sourceIcon
    }

    // 3. 尝试常见的favicon路径
    const commonIcon = await this.findCommonFaviconPaths()
    if (commonIcon) {
      console.log('【ContentFaviconService】从常见路径找到图标:', commonIcon)
      return commonIcon
    }

    console.log('【ContentFaviconService】未找到有效的favicon')
    return null
  }

  /**
   * 从HTML head中获取favicon候选列表，按优先级排序
   */
  private getFaviconCandidatesFromHead(): Array<{ url: string; type: string; priority: number }> {
    const candidates: Array<{ url: string; type: string; priority: number }> = []
    
    // 查找所有可能的图标链接
    const iconSelectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="apple-touch-icon-precomposed"]',
      'link[rel="mask-icon"]',
      'link[rel="fluid-icon"]'
    ]

    iconSelectors.forEach(selector => {
      const links = document.querySelectorAll(selector)
      links.forEach(link => {
        const href = (link as HTMLLinkElement).href
        const type = (link as HTMLLinkElement).type || ''
        const sizes = (link as HTMLLinkElement).sizes?.toString() || ''
        
        if (href && this.isValidFaviconUrl(href)) {
          candidates.push({
            url: href,
            type: type,
            priority: this.calculateFaviconPriority(type, sizes, selector)
          })
        }
      })
    })

    // 按优先级排序（优先级越高越靠前）
    return candidates.sort((a, b) => b.priority - a.priority)
  }

  /**
   * 计算favicon的优先级
   */
  private calculateFaviconPriority(type: string, sizes: string, selector: string): number {
    let priority = 0
    
    // 根据MIME类型评分
    if (type.includes('svg')) {
      priority += 100 // SVG最高优先级，矢量图标
    } else if (type.includes('png')) {
      priority += 80 // PNG次之，支持透明
    } else if (type.includes('webp')) {
      priority += 70 // WebP现代格式
    } else if (type.includes('ico')) {
      priority += 60 // ICO传统格式
    } else if (type.includes('jpeg') || type.includes('jpg')) {
      priority += 50 // JPEG较低优先级
    }
    
    // 根据尺寸评分
    if (sizes) {
      const sizeNumbers = sizes.match(/\d+/g)?.map(Number) || []
      const maxSize = Math.max(...sizeNumbers)
      if (maxSize >= 192) {
        priority += 30 // 高分辨率
      } else if (maxSize >= 64) {
        priority += 20 // 中等分辨率
      } else if (maxSize >= 32) {
        priority += 10 // 标准分辨率
      }
    }
    
    // 根据rel类型评分
    if (selector.includes('rel="icon"')) {
      priority += 40 // 标准icon最高
    } else if (selector.includes('apple-touch-icon')) {
      priority += 30 // Apple图标次之
    } else if (selector.includes('shortcut')) {
      priority += 20 // 传统shortcut icon
    }
    
    return priority
  }

  /**
   * 从页面Sources中查找图标资源
   */
  private async findIconFromSources(): Promise<string | null> {
    try {
      // 查找页面中可能的图标元素
      const iconSelectors = [
        'img[src*="logo"]',
        'img[src*="icon"]',
        'img[alt*="logo"]',
        'img[alt*="icon"]',
        '.logo img',
        '.icon img',
        '[class*="logo"] img',
        '[class*="icon"] img'
      ]
      
      for (const selector of iconSelectors) {
        const imgs = document.querySelectorAll(selector)
        for (const img of imgs) {
          const src = (img as HTMLImageElement).src
          if (src && this.isValidFaviconUrl(src) && await this.testFaviconUrl(src)) {
            return src
          }
        }
      }
      
      return null
    } catch (error) {
      console.error('【ContentFaviconService】从Sources查找图标失败:', error)
      return null
    }
  }

  /**
   * 尝试常见的favicon路径
   */
  private async findCommonFaviconPaths(): Promise<string | null> {
    const commonPaths = [
      '/favicon.svg',
      '/favicon.png', 
      '/favicon.ico',
      '/apple-touch-icon.png',
      '/apple-touch-icon-192x192.png',
      '/apple-touch-icon-180x180.png',
      '/favicon-32x32.png',
      '/favicon-16x16.png',
      '/icon-192x192.png',
      '/icon-512x512.png'
    ]

    const baseUrl = `${window.location.protocol}//${window.location.host}`
    
    for (const path of commonPaths) {
      const faviconUrl = baseUrl + path
      if (await this.testFaviconUrl(faviconUrl)) {
        return faviconUrl
      }
    }

    return null
  }

  /**
   * 测试favicon URL是否可访问
   */
  private async testFaviconUrl(url: string): Promise<boolean> {
    try {
      // 使用AbortController实现超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时
      
      try {
        const response = await fetch(url, { 
          method: 'HEAD',
          signal: controller.signal,
          cache: 'no-cache'
        })
        
        clearTimeout(timeoutId)
        
        if (!response.ok) {
          return false
        }
        
        // 检查Content-Type
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.startsWith('image/')) {
          return true
        }
        
        // 如果没有Content-Type或不是图片类型，但状态码正常，也认为可能有效
        // 某些服务器可能不返回正确的Content-Type
        return true
        
      } catch (fetchError) {
        clearTimeout(timeoutId)
        
        // 如果是超时错误，记录日志
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          console.log('【ContentFaviconService】favicon URL测试超时:', url)
        }
        
        return false
      }
    } catch (error) {
      console.log('【ContentFaviconService】favicon URL测试异常:', url, error)
      return false
    }
  }

  /**
   * 验证favicon URL是否有效
   */
  private isValidFaviconUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname.toLowerCase()
      
      // 检查是否为图片格式
      const validExtensions = ['.ico', '.png', '.svg', '.jpg', '.jpeg', '.gif', '.webp']
      return validExtensions.some(ext => pathname.endsWith(ext)) || 
             pathname.includes('favicon') || 
             pathname.includes('icon')
    } catch {
      return false
    }
  }

  /**
   * 验证blob是否为有效图片
   */
  private isValidImageBlob(blob: Blob): boolean {
    try {
      // 1. 检查基本属性
      if (!blob || blob.size === 0) {
        console.log('【ContentFaviconService】Blob验证失败: 空数据')
        return false
      }

      // 2. 检查文件大小限制
      const minSize = 100 // 最小100字节
      const maxSize = 2 * 1024 * 1024 // 最大2MB
      if (blob.size < minSize) {
        console.log('【ContentFaviconService】Blob验证失败: 文件太小', blob.size)
        return false
      }
      if (blob.size > maxSize) {
        console.log('【ContentFaviconService】Blob验证失败: 文件太大', blob.size)
        return false
      }

      // 3. 验证MIME类型
      const validTypes = [
        'image/x-icon',
        'image/vnd.microsoft.icon',
        'image/png',
        'image/svg+xml',
        'image/jpeg',
        'image/jpg',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/tiff'
      ]
      
      if (!validTypes.includes(blob.type)) {
        console.log('【ContentFaviconService】Blob验证失败: 不支持的MIME类型', blob.type)
        return false
      }

      // 4. 对于特定格式进行额外验证
      if (blob.type === 'image/svg+xml') {
        // SVG文件大小通常较小，但可能很大
        if (blob.size > 512 * 1024) { // SVG限制512KB
          console.log('【ContentFaviconService】SVG文件过大:', blob.size)
          return false
        }
      }

      if (blob.type.includes('png') || blob.type.includes('jpeg') || blob.type.includes('jpg')) {
        // 位图格式的合理大小检查
        if (blob.size < 200) { // 位图至少200字节
          console.log('【ContentFaviconService】位图文件过小:', blob.size)
          return false
        }
      }

      console.log('【ContentFaviconService】Blob验证通过:', {
        type: blob.type,
        size: blob.size
      })
      
      return true
    } catch (error) {
      console.error('【ContentFaviconService】Blob验证异常:', error)
      return false
    }
  }

  /**
   * 异步验证blob数据完整性（可选的深度验证）
   */
  private async validateBlobIntegrity(blob: Blob): Promise<boolean> {
    try {
      // 对于非SVG格式，尝试创建Image对象验证
      if (!blob.type.includes('svg')) {
        return new Promise<boolean>((resolve) => {
          const img = new Image()
          const url = URL.createObjectURL(blob)
          
          const cleanup = () => {
            URL.revokeObjectURL(url)
          }
          
          img.onload = () => {
            cleanup()
            // 检查图片尺寸是否合理
            if (img.width > 0 && img.height > 0 && img.width <= 1024 && img.height <= 1024) {
              resolve(true)
            } else {
              console.log('【ContentFaviconService】图片尺寸异常:', img.width, 'x', img.height)
              resolve(false)
            }
          }
          
          img.onerror = () => {
            cleanup()
            console.log('【ContentFaviconService】图片加载失败，可能已损坏')
            resolve(false)
          }
          
          // 设置超时
          setTimeout(() => {
            cleanup()
            console.log('【ContentFaviconService】图片验证超时')
            resolve(false)
          }, 3000)
          
          img.src = url
        })
      }
      
      // SVG格式简单验证
      if (blob.type.includes('svg')) {
        const text = await blob.text()
        return text.includes('<svg') && text.includes('</svg>')
      }
      
      return true
    } catch (error) {
      console.error('【ContentFaviconService】Blob完整性验证异常:', error)
      return false
    }
  }

  /**
   * 将Blob转换为Data URL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 通过消息传递更新平台favicon（从content script调用）
   */
  async updateCurrentPlatformFavicon(platformId: number, retryCount: number = 0): Promise<boolean> {
    const maxRetries = 2
    
    try {
      console.log('【ContentFaviconService】开始更新平台favicon:', platformId, retryCount > 0 ? `(重试 ${retryCount}/${maxRetries})` : '')
      
      // 获取当前页面的favicon
      const faviconResult = await this.getCurrentPageFavicon()
      if (!faviconResult.success || !faviconResult.blob) {
        console.error('【ContentFaviconService】获取favicon失败:', faviconResult.error)
        
        // 如果是网络错误且还有重试次数，则重试
        if (retryCount < maxRetries && this.isRetryableError(faviconResult.error)) {
          console.log('【ContentFaviconService】检测到可重试错误，准备重试...')
          await this.delay(1000 * (retryCount + 1)) // 递增延迟
          return this.updateCurrentPlatformFavicon(platformId, retryCount + 1)
        }
        
        return false
      }

      // 通过消息传递发送到background script
      const response = await MessagingService.sendToBackground(MessageType.UPDATE_PLATFORM_FAVICON, {
        platformId,
        faviconBlob: faviconResult.blob,
        faviconUrl: faviconResult.source
      })

      if (response.success) {
        console.log('【ContentFaviconService】成功更新平台favicon')
        
        // 清理iconBlobService的缓存，确保下次获取时使用最新数据
        const { iconBlobService } = await import('./iconBlobService')
        iconBlobService.clearPlatformCache(platformId)
        console.log('【ContentFaviconService】已清理iconBlobService缓存')
        
        return true
      } else {
        console.error('【ContentFaviconService】更新平台favicon失败:', response.error)
        
        // 如果是通信错误且还有重试次数，则重试
        if (retryCount < maxRetries && this.isRetryableError(response.error)) {
          console.log('【ContentFaviconService】检测到可重试的通信错误，准备重试...')
          await this.delay(1000 * (retryCount + 1))
          return this.updateCurrentPlatformFavicon(platformId, retryCount + 1)
        }
        
        return false
      }

    } catch (error) {
      console.error('【ContentFaviconService】更新平台favicon异常:', error)
      
      // 如果是网络异常且还有重试次数，则重试
      if (retryCount < maxRetries && this.isRetryableError(error)) {
        console.log('【ContentFaviconService】检测到可重试异常，准备重试...')
        await this.delay(1000 * (retryCount + 1))
        return this.updateCurrentPlatformFavicon(platformId, retryCount + 1)
      }
      
      return false
    }
  }

  /**
   * 判断错误是否可重试
   */
  private isRetryableError(error: any): boolean {
    if (!error) return false
    
    const errorStr = error.toString().toLowerCase()
    const retryableErrors = [
      'network error',
      'fetch error',
      'timeout',
      'connection',
      'failed to fetch',
      'networkerror',
      'net::err_',
      'http 5', // 5xx服务器错误
      'http 429' // 限流错误
    ]
    
    return retryableErrors.some(pattern => errorStr.includes(pattern))
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 检查当前页面是否需要更新favicon
   */
  async checkAndUpdateFaviconIfNeeded(platformId: number): Promise<boolean> {
    const startTime = Date.now()
    ContentFaviconService.stats.totalRequests++
    
    try {
      // 检查是否已在批量队列中
      const existingPromise = ContentFaviconService.batchQueue.get(platformId)
      if (existingPromise) {
        console.log('【ContentFaviconService】平台favicon已在批量队列中，复用Promise:', platformId)
        return existingPromise
      }

      // 检查是否正在下载中
      if (ContentFaviconService.downloadingPlatforms.has(platformId)) {
        console.log('【ContentFaviconService】平台', platformId, '正在下载favicon中，跳过重复请求')
        return true // 正在下载中，直接返回成功
      }

      // 检查缓存
      const cached = ContentFaviconService.checkCache.get(platformId)
      if (cached && (Date.now() - cached.timestamp) < ContentFaviconService.CACHE_DURATION) {
        console.log('【ContentFaviconService】使用缓存结果，平台', platformId, '检查结果:', cached.result)
        ContentFaviconService.stats.cacheHits++
        this.updateProcessTime(startTime)
        return cached.result
      }

      // 创建处理Promise并加入批量队列
      const processPromise = this.processFaviconUpdate(platformId, startTime)
      ContentFaviconService.batchQueue.set(platformId, processPromise)

      // 设置批量处理定时器
      if (!ContentFaviconService.batchTimer) {
        ContentFaviconService.batchTimer = setTimeout(() => {
          this.processBatch()
        }, ContentFaviconService.BATCH_DELAY)
      }

      return processPromise
    } catch (error) {
      console.error('【ContentFaviconService】检查favicon更新需求失败:', error)
      ContentFaviconService.stats.failedUpdates++
      this.updateProcessTime(startTime)
      return false
    }
  }

  /**
   * 更新处理时间统计
   */
  private updateProcessTime(startTime: number): void {
    const processTime = Date.now() - startTime
    const currentAvg = ContentFaviconService.stats.averageProcessTime
    const totalRequests = ContentFaviconService.stats.totalRequests
    
    // 计算新的平均处理时间
    ContentFaviconService.stats.averageProcessTime = 
      (currentAvg * (totalRequests - 1) + processTime) / totalRequests
  }

  /**
   * 处理单个favicon更新
   */
  private async processFaviconUpdate(platformId: number, startTime?: number): Promise<boolean> {
    try {
      // 清理过期缓存
      ContentFaviconService.cleanExpiredCache()

      // 通过消息传递检查平台是否需要更新favicon
      const response = await MessagingService.sendToBackground(MessageType.CHECK_PLATFORM_FAVICON, {
        platformId
      })

      if (response.success && response.data?.needsUpdate) {
        console.log('【ContentFaviconService】检测到需要更新favicon，原因:', response.data.reason || 'unknown')
        
        // 添加到下载中集合
        ContentFaviconService.downloadingPlatforms.add(platformId)
        
        try {
          const updateResult = await this.updateCurrentPlatformFavicon(platformId)
          
          // 更新统计
          if (updateResult) {
            ContentFaviconService.stats.successfulUpdates++
          } else {
            ContentFaviconService.stats.failedUpdates++
          }
          
          // 缓存结果
          ContentFaviconService.checkCache.set(platformId, {
            result: updateResult,
            timestamp: Date.now()
          })
          
          return updateResult
        } finally {
          // 无论成功失败都要移除下载标记
          ContentFaviconService.downloadingPlatforms.delete(platformId)
          ContentFaviconService.batchQueue.delete(platformId)
          
          // 更新处理时间统计
          if (startTime) {
            this.updateProcessTime(startTime)
          }
        }
      }

      // 不需要更新，缓存结果
      ContentFaviconService.checkCache.set(platformId, {
        result: true,
        timestamp: Date.now()
      })
      
      // 从批量队列中移除
      ContentFaviconService.batchQueue.delete(platformId)
      
      return true // 不需要更新也算成功
      
    } catch (error) {
      console.error('【ContentFaviconService】处理favicon更新失败:', platformId, error)
      
      // 确保移除下载标记和批量队列
      ContentFaviconService.downloadingPlatforms.delete(platformId)
      ContentFaviconService.batchQueue.delete(platformId)
      
      // 缓存失败结果（较短时间）
      ContentFaviconService.checkCache.set(platformId, {
        result: false,
        timestamp: Date.now()
      })
      
      return false
    }
  }

  /**
   * 处理批量队列
   */
  private processBatch(): void {
    console.log('【ContentFaviconService】处理批量favicon队列，当前队列大小:', ContentFaviconService.batchQueue.size)
    
    // 重置定时器
    ContentFaviconService.batchTimer = null
    
    // 如果队列不为空，设置下一次批量处理
    if (ContentFaviconService.batchQueue.size > 0) {
      ContentFaviconService.batchTimer = setTimeout(() => {
        this.processBatch()
      }, ContentFaviconService.BATCH_DELAY)
    }
  }

  /**
   * 获取性能统计信息
   */
  static getPerformanceStats() {
    const stats = { ...ContentFaviconService.stats }
    const now = Date.now()
    const runtimeMinutes = (now - stats.lastResetTime) / (1000 * 60)
    
    return {
      ...stats,
      runtimeMinutes: Math.round(runtimeMinutes * 100) / 100,
      cacheHitRate: stats.totalRequests > 0 ? 
        Math.round((stats.cacheHits / stats.totalRequests) * 10000) / 100 : 0,
      successRate: (stats.successfulUpdates + stats.failedUpdates) > 0 ? 
        Math.round((stats.successfulUpdates / (stats.successfulUpdates + stats.failedUpdates)) * 10000) / 100 : 0,
      averageProcessTimeMs: Math.round(stats.averageProcessTime * 100) / 100,
      currentCacheSize: ContentFaviconService.checkCache.size,
      currentQueueSize: ContentFaviconService.batchQueue.size,
      downloadingCount: ContentFaviconService.downloadingPlatforms.size
    }
  }

  /**
   * 重置性能统计
   */
  static resetPerformanceStats(): void {
    ContentFaviconService.stats = {
      totalRequests: 0,
      cacheHits: 0,
      successfulUpdates: 0,
      failedUpdates: 0,
      averageProcessTime: 0,
      lastResetTime: Date.now()
    }
    console.log('【ContentFaviconService】性能统计已重置')
  }

  /**
   * 打印性能统计报告
   */
  static logPerformanceReport(): void {
    const stats = ContentFaviconService.getPerformanceStats()
    console.log('【ContentFaviconService】性能统计报告:')
    console.log('  运行时间:', stats.runtimeMinutes, '分钟')
    console.log('  总请求数:', stats.totalRequests)
    console.log('  缓存命中数:', stats.cacheHits)
    console.log('  缓存命中率:', stats.cacheHitRate + '%')
    console.log('  成功更新数:', stats.successfulUpdates)
    console.log('  失败更新数:', stats.failedUpdates)
    console.log('  更新成功率:', stats.successRate + '%')
    console.log('  平均处理时间:', stats.averageProcessTimeMs, 'ms')
    console.log('  当前缓存大小:', stats.currentCacheSize)
    console.log('  当前队列大小:', stats.currentQueueSize)
    console.log('  正在下载数:', stats.downloadingCount)
  }

  /**
   * 获取当前页面信息（用于调试）
   */
  /**
   * 清理过期的缓存数据
   */
  private static cleanExpiredCache(): void {
    const now = Date.now()
    for (const [platformId, cache] of ContentFaviconService.checkCache.entries()) {
      if (now - cache.timestamp > ContentFaviconService.CACHE_DURATION) {
        ContentFaviconService.checkCache.delete(platformId)
      }
    }
  }

  /**
   * 获取当前页面信息（用于调试）
   */
  getCurrentPageInfo(): {
    url: string
    hostname: string
    faviconLinks: string[]
  } {
    const faviconLinks = Array.from(document.querySelectorAll('link[rel*="icon"]'))
      .map(link => (link as HTMLLinkElement).href)
      .filter(href => href)

    return {
      url: window.location.href,
      hostname: window.location.hostname,
      faviconLinks
    }
  }
}

// 导出单例实例
export const contentFaviconService = ContentFaviconService.getInstance()
