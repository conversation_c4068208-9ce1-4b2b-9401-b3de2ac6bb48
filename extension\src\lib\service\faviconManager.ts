/**
 * 统一的Favicon管理服务
 * 简化favicon处理逻辑，集成平台检测、favicon提取、数据存储功能
 */

import { Platform } from '@/types/database'
import { MessageType } from '@/types'
import { MessagingService } from './messagingService'

export interface FaviconResult {
  success: boolean
  blob?: Blob
  dataUrl?: string
  error?: string
  source?: string
}

export class FaviconManager {
  private static instance: FaviconManager
  private cache: Map<number, boolean> = new Map() // platformId -> hasValidIcon
  private processing: Set<number> = new Set() // 正在处理的平台ID
  private readonly CACHE_DURATION = 30000 // 30秒缓存时间
  private cacheTimestamps: Map<number, number> = new Map()

  // 支持的favicon路径，按优先级排序
  private readonly FAVICON_PATHS = [
    '/favicon.svg',
    '/favicon.png', 
    '/favicon.ico',
    '/apple-touch-icon.png',
    '/favicon-32x32.png',
    '/favicon-16x16.png'
  ]

  // 支持的MIME类型
  private readonly SUPPORTED_TYPES = [
    'image/svg+xml',
    'image/png',
    'image/x-icon',
    'image/vnd.microsoft.icon',
    'image/jpeg',
    'image/webp'
  ]

  private constructor() {}

  static getInstance(): FaviconManager {
    if (!FaviconManager.instance) {
      FaviconManager.instance = new FaviconManager()
    }
    return FaviconManager.instance
  }

  /**
   * 检查并更新当前页面的favicon
   */
  async checkAndUpdateFavicon(): Promise<boolean> {
    try {
      const platform = await this.detectCurrentPlatform()
      if (!platform) return false

      const platformId = platform.id!

      if (this.isCached(platformId)) {
        return this.cache.get(platformId) || false
      }

      if (this.processing.has(platformId)) return false

      this.processing.add(platformId)

      try {
        const needsUpdate = await this.checkIfNeedsUpdate(platformId)
        if (!needsUpdate) {
          this.updateCache(platformId, true)
          return true
        }

        const result = await this.updatePlatformFavicon(platformId)
        this.updateCache(platformId, result)
        return result

      } finally {
        this.processing.delete(platformId)
      }

    } catch (error) {
      console.error('【FaviconManager】检查更新favicon失败:', error)
      return false
    }
  }

  /**
   * 检测当前平台
   */
  private async detectCurrentPlatform(): Promise<Platform | null> {
    try {
      const response = await MessagingService.sendToBackground(
        MessageType.DB_PLATFORM_GET_BY_DOMAIN,
        { hostname: window.location.hostname }
      )
      return response.success ? response.data : null
    } catch (error) {
      console.error('【FaviconManager】检测平台失败:', error)
      return null
    }
  }

  /**
   * 检查平台是否需要更新favicon
   */
  private async checkIfNeedsUpdate(platformId: number): Promise<boolean> {
    try {
      const response = await MessagingService.sendToBackground(
        MessageType.CHECK_PLATFORM_FAVICON,
        { platformId }
      )
      return response.success && response.data?.needsUpdate
    } catch (error) {
      console.error('【FaviconManager】检查更新需求失败:', error)
      return false
    }
  }

  /**
   * 更新平台favicon
   */
  private async updatePlatformFavicon(platformId: number): Promise<boolean> {
    try {
      const faviconResult = await this.getCurrentPageFavicon()
      if (!faviconResult.success || !faviconResult.blob) {
        console.error('【FaviconManager】获取favicon失败:', faviconResult.error)
        return false
      }

      // 将Blob转换为ArrayBuffer进行传输
      const arrayBuffer = await faviconResult.blob.arrayBuffer()
      const response = await MessagingService.sendToBackground(
        MessageType.UPDATE_PLATFORM_FAVICON,
        {
          platformId,
          faviconData: Array.from(new Uint8Array(arrayBuffer)),
          faviconType: faviconResult.blob.type,
          faviconUrl: faviconResult.source
        }
      )

      if (response.success) {
        console.log('【FaviconManager】成功更新平台favicon')
        return true
      } else {
        console.error('【FaviconManager】更新平台favicon失败:', response.error)
        return false
      }

    } catch (error) {
      console.error('【FaviconManager】更新favicon异常:', error)
      return false
    }
  }

  /**
   * 获取当前页面的favicon
   */
  async getCurrentPageFavicon(): Promise<FaviconResult> {
    try {
      const headFavicon = this.findFaviconFromHead()
      if (headFavicon) {
        const result = await this.fetchFavicon(headFavicon)
        if (result.success) return result
      }

      const baseUrl = `${window.location.protocol}//${window.location.host}`
      for (const path of this.FAVICON_PATHS) {
        const result = await this.fetchFavicon(baseUrl + path)
        if (result.success) return { ...result, source: baseUrl + path }
      }

      return { success: false, error: 'No valid favicon found' }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 从HTML head中查找favicon
   */
  private findFaviconFromHead(): string | null {
    const selectors = ['link[rel="icon"]', 'link[rel="shortcut icon"]', 'link[rel="apple-touch-icon"]']

    for (const selector of selectors) {
      const link = document.querySelector(selector) as HTMLLinkElement
      if (link?.href && this.isValidFaviconUrl(link.href)) return link.href
    }
    return null
  }

  /**
   * 获取favicon数据
   */
  private async fetchFavicon(url: string): Promise<FaviconResult> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      const response = await fetch(url, { signal: controller.signal, cache: 'no-cache' })
      clearTimeout(timeoutId)

      if (!response.ok) return { success: false, error: `HTTP ${response.status}` }

      const blob = await response.blob()
      if (!this.validateFavicon(blob)) return { success: false, error: 'Invalid favicon data' }

      const dataUrl = await this.blobToDataUrl(blob)
      return { success: true, blob, dataUrl, source: url }

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Fetch failed' }
    }
  }

  /**
   * 验证favicon有效性
   */
  private validateFavicon(blob: Blob): boolean {
    if (blob.size === 0 || blob.size > 1024 * 1024) return false
    return this.SUPPORTED_TYPES.some(type => blob.type.toLowerCase().includes(type.toLowerCase()))
  }

  /**
   * 验证favicon URL
   */
  private isValidFaviconUrl(url: string): boolean {
    try {
      const pathname = new URL(url).pathname.toLowerCase()
      const validExtensions = ['.ico', '.png', '.svg', '.jpg', '.jpeg', '.webp']
      return validExtensions.some(ext => pathname.endsWith(ext)) ||
             pathname.includes('favicon') || pathname.includes('icon')
    } catch {
      return false
    }
  }

  /**
   * Blob转DataURL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 检查缓存
   */
  private isCached(platformId: number): boolean {
    const timestamp = this.cacheTimestamps.get(platformId)
    return timestamp ? Date.now() - timestamp < this.CACHE_DURATION : false
  }

  /**
   * 更新缓存
   */
  private updateCache(platformId: number, result: boolean): void {
    this.cache.set(platformId, result)
    this.cacheTimestamps.set(platformId, Date.now())
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
    this.processing.clear()
  }
}

// 导出单例实例
export const faviconManager = FaviconManager.getInstance()
