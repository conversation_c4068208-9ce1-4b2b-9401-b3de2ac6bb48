// 数据库基础类型
export interface DatabaseRow {
  id: number
  created_at?: number
  updated_at?: number
}

// 平台表类型
export interface Platform extends DatabaseRow {
  name: string
  url: string
  icon?: string
  icon_blob?: Blob // 新增：存储favicon的BLOB数据
  is_delete: number
}

// 平台创建输入类型
export interface CreatePlatformInput {
  name: string
  url: string
  icon?: string
  icon_blob?: Blob
}

// 平台更新输入类型
export interface UpdatePlatformInput {
  name?: string
  url?: string
  icon?: string
  icon_blob?: Blob
  is_delete?: number
}

// 聊天历史表类型
export interface ChatHistory extends DatabaseRow {
  chat_prompt: string
  chat_answer?: string
  chat_uid: string
  platform_id: number
  tags?: string // JSON字符串，存储标签数组
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time: number
  is_synced: number
  is_delete: number
}

// 聊天历史创建输入类型
export interface CreateChatHistoryInput {
  chat_prompt: string
  chat_answer?: string
  chat_uid: string
  platform_id: number
  tags?: string[]
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time?: number
}

// 聊天历史更新输入类型
export interface UpdateChatHistoryInput {
  chat_prompt?: string
  chat_answer?: string
  chat_uid?: string
  platform_id?: number
  tags?: string[]
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  is_synced?: number
  is_delete?: number
}

// 聊天历史查询参数类型
export interface ChatHistoryQueryParams {
  platform_id?: number
  chat_uid?: string
  chat_group_name?: string
  is_synced?: number
  is_delete?: number
  limit?: number
  offset?: number
  page?: number
  order_by?: 'create_time' | 'id'
  order_direction?: 'ASC' | 'DESC'
  search?: string // 全文搜索关键词
}

// 聊天历史统计类型
export interface ChatHistoryStats {
  total_count: number
  platform_counts: { [platform_name: string]: number }
  recent_count: number // 最近7天的数量
}

// 聊天历史与平台的联合查询结果
export interface ChatHistoryWithPlatform extends ChatHistory {
  platform_name: string
  platform_url: string
  platform_icon?: string
  platform_icon_blob?: Blob
}

// 数据库操作结果类型
export interface DatabaseResult<T = any> {
  success: boolean
  data?: T
  error?: string
  affected_rows?: number
  last_insert_id?: number
}

// 分页查询结果类型
export interface PaginatedResult<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 全文搜索结果类型
export interface SearchResult<T> extends PaginatedResult<T> {
  searchTerm: string
}

// 数据库迁移类型
export interface Migration {
  version: number
  name: string
  up: string[] // 升级SQL语句
  down: string[] // 回滚SQL语句
}

// 数据库配置类型
export interface DatabaseConfig {
  path: string
  timeout: number
  max_connections: number
  enable_wal: boolean
  enable_foreign_keys: boolean
}

// 事务回调类型
export type TransactionCallback<T> = () => Promise<T>

// 数据库事件类型
export type DatabaseEvent = 
  | 'connected'
  | 'disconnected'
  | 'error'
  | 'migration_start'
  | 'migration_complete'
  | 'migration_error'

// 数据库事件监听器类型
export type DatabaseEventListener = (event: DatabaseEvent, data?: any) => void
