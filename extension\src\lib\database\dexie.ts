import Dexie, { Table } from 'dexie'
import { ChatHistory, Platform } from '@/types/database'

export interface ChatHistoryWithPlatform extends ChatHistory {
  platform_name: string
  platform_url: string
  platform_icon: string
  platform_icon_blob?: Blob
}

export class EchoSyncDatabase extends <PERSON>ie {
  // 定义表
  chatHistory!: Table<ChatHistory>
  platform!: Table<Platform>

  constructor() {
    super('EchoSyncDatabase')
    
    // 定义数据库结构
    this.version(1).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced',
      platform: '++id, name, url, is_delete'
    })

    // 添加索引以提高查询性能
    this.version(2).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 2...')
    })

    // 添加chat_prompt索引以支持跨平台UID共享
    this.version(3).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 3 - adding chat_prompt index...')
    })

    // 添加icon_blob字段支持BLOB存储
    this.version(4).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 4 - adding icon_blob support...')
      // 注意：Dexie会自动处理新字段的添加，无需手动迁移
    })
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      console.log('【EchoSync】Starting database initialization...')
      await this.open()
      console.log('【EchoSync】Database opened successfully')

      await this.insertDefaultPlatforms()
      console.log('【EchoSync】Default platforms inserted')

      // 验证数据库是否正常工作
      const platformCount = await this.platform.count()
      const chatHistoryCount = await this.chatHistory.count()
      console.log('【EchoSync】Database verification - Platforms:', platformCount, 'ChatHistory:', chatHistoryCount)

      console.log('【EchoSync】Dexie database initialized successfully')
    } catch (error) {
      console.error('【EchoSync】Database initialization failed:', error)
      console.error('【EchoSync】Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })
      throw error
    }
  }

  /**
   * 插入默认平台数据
   */
  private async insertDefaultPlatforms(): Promise<void> {
    const defaultPlatforms: Platform[] = [
      {
        id: 1,
        name: 'DeepSeek',
        url: 'https://chat.deepseek.com',
        icon: 'https://chat.deepseek.com/favicon.ico',
        is_delete: 0
      },
      {
        id: 2,
        name: 'Kimi',
        url: 'https://kimi.moonshot.cn',
        icon: 'https://kimi.moonshot.cn/favicon.ico',
        is_delete: 0
      },
      {
        id: 3,
        name: 'ChatGPT',
        url: 'https://chat.openai.com',
        icon: 'https://chat.openai.com/favicon.ico',
        is_delete: 0
      },
      {
        id: 4,
        name: 'Claude',
        url: 'https://claude.ai',
        icon: 'https://claude.ai/favicon.ico',
        is_delete: 0
      },
      {
        id: 5,
        name: 'Gemini',
        url: 'https://gemini.google.com',
        icon: 'https://gemini.google.com/favicon.ico',
        is_delete: 0
      }
    ]

    // 逐个检查并插入平台数据，确保ID固定
    for (const platform of defaultPlatforms) {
      const existing = await this.platform.get(platform.id)
      if (!existing) {
        await this.platform.put(platform)
        console.log(`Platform ${platform.name} (ID: ${platform.id}) inserted`)
      }
    }
  }

  /**
   * 获取聊天历史（带平台信息）
   */
  async getChatHistoryWithPlatform(options: {
    limit?: number
    offset?: number
    platform_id?: number
    order_by?: 'create_time' | 'id'
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<ChatHistoryWithPlatform[]> {
    const {
      limit = 50,
      offset = 0,
      platform_id,
      order_by = 'create_time',
      order_direction = 'DESC'
    } = options

    let query = this.chatHistory
      .where('is_delete')
      .equals(0)

    if (platform_id) {
      query = query.and(item => item.platform_id === platform_id)
    }

    const chatHistories = await query
      .offset(offset)
      .limit(limit)
      .reverse() // Dexie 中的倒序
      .toArray()

    // 获取平台信息并合并
    const platformIds = [...new Set(chatHistories.map(ch => ch.platform_id))]
    const platforms = await this.platform
      .where('id')
      .anyOf(platformIds)
      .toArray()

    const platformMap = new Map(platforms.map(p => [p.id!, p]))

    return chatHistories.map(ch => {
      const platform = platformMap.get(ch.platform_id)

      // 添加调试日志
      if (platform && platform.icon_blob) {
        console.log(`【DexieDatabase】平台${platform.name}的icon_blob信息:`, {
          type: typeof platform.icon_blob,
          constructor: platform.icon_blob.constructor?.name,
          isBlob: platform.icon_blob instanceof Blob,
          size: platform.icon_blob.size || 'N/A',
          keys: Object.keys(platform.icon_blob)
        })
      }

      return {
        ...ch,
        platform_name: platform?.name || 'Unknown',
        platform_url: platform?.url || '',
        platform_icon: platform?.icon || '',
        platform_icon_blob: platform?.icon_blob
      }
    })
  }

  /**
   * 获取去重的聊天历史
   */
  async getUniqueChats(options: {
    limit?: number
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<ChatHistoryWithPlatform[]> {
    const { limit = 20, order_direction = 'DESC' } = options

    // 获取所有未删除的聊天记录
    const allChats = await this.getChatHistoryWithPlatform({
      limit: 1000, // 先获取更多数据用于去重
      order_direction
    })

    // 按 chat_uid 去重，保留最新的
    const uniqueChatsMap = new Map<string, ChatHistoryWithPlatform>()
    
    for (const chat of allChats) {
      const existing = uniqueChatsMap.get(chat.chat_uid)
      if (!existing || chat.create_time > existing.create_time) {
        uniqueChatsMap.set(chat.chat_uid, chat)
      }
    }

    // 转换为数组并排序
    const uniqueChats = Array.from(uniqueChatsMap.values())
    uniqueChats.sort((a, b) => {
      return order_direction === 'DESC' 
        ? b.create_time - a.create_time
        : a.create_time - b.create_time
    })

    return uniqueChats.slice(0, limit)
  }

  /**
   * 全文搜索聊天历史
   */
  async searchChatHistory(searchTerm: string, options: {
    limit?: number
    platform_id?: number
  } = {}): Promise<ChatHistoryWithPlatform[]> {
    const { limit = 50, platform_id } = options

    // Dexie 不支持全文搜索，使用简单的文本匹配
    let query = this.chatHistory
      .where('is_delete')
      .equals(0)

    if (platform_id) {
      query = query.and(item => item.platform_id === platform_id)
    }

    const results = await query
      .filter(item => 
        item.chat_prompt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.chat_answer && item.chat_answer.toLowerCase().includes(searchTerm.toLowerCase()))
      )
      .limit(limit)
      .toArray()

    // 获取平台信息并合并
    const platformIds = [...new Set(results.map(ch => ch.platform_id))]
    const platforms = await this.platform
      .where('id')
      .anyOf(platformIds)
      .toArray()

    const platformMap = new Map(platforms.map(p => [p.id!, p]))

    return results.map(ch => {
      const platform = platformMap.get(ch.platform_id)
      return {
        ...ch,
        platform_name: platform?.name || 'Unknown',
        platform_url: platform?.url || '',
        platform_icon: platform?.icon || '',
        platform_icon_blob: platform?.icon_blob
      }
    })
  }
}

// 导出单例实例
export const dexieDatabase = new EchoSyncDatabase()
